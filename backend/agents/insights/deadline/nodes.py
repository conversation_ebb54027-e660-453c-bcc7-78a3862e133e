"""
Deadline Insights Agent Node Implementations

This module contains the implementation of the nodes for the Deadline Insights Agent.
Each node is a function that takes a state and returns an updated state or a command
to transition to another node.

The nodes implement the deadline insights workflow, including:
- Data fetching and validation
- Conflict detection algorithms
- Risk assessment calculations
- Batch processing coordination
- MCP Rules Engine integration
"""

import asyncio
import hashlib
import json
import logging
import os
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

try:
    from langchain_core.output_parsers.json import JsonOutputParser
    from langchain_core.runnables import RunnableConfig
    from langgraph.types import Command
except ImportError:
    # Fallback for environments without LangChain/LangGraph
    JsonOutputParser = None
    RunnableConfig = None
    Command = None

from pydantic import ValidationError

from .state import (
    DeadlineInsightsState,
    AnalysisType,
    ConflictSeverity,
    DeadlineConflict,
    DeadlineAnalysis,
    ANALYZE_DEADLINES_FN_SCHEMA
)
from .database import DeadlineRepository

# Set up logging
logger = logging.getLogger(__name__)

# Define constants
DEFAULT_BATCH_SIZE = int(os.getenv("DEADLINE_BATCH_SIZE", "50"))
MAX_CONCURRENT_BATCHES = int(os.getenv("DEADLINE_MAX_CONCURRENT_BATCHES", "3"))
CACHE_TTL_SECONDS = int(os.getenv("DEADLINE_ANALYSIS_CACHE_TTL", "600"))
ENABLE_MCP_INTEGRATION = os.getenv("FEATURE_MCP_RULES_ENGINE", "true").lower() == "true"


class DeadlineInsightsNodes:
    """Node implementations for Deadline Insights Agent."""

    def __init__(self):
        """Initialize the deadline insights nodes."""
        self.parser = JsonOutputParser() if JsonOutputParser else None
        self._cache = {}
        self._cache_max_size = 1000
        self._cache_ttl_ms = CACHE_TTL_SECONDS * 1000
        self._repositories = {}  # Cache repositories by tenant_id

        logger.info("Initialized Deadline Insights Nodes")

    def _get_repository(self, tenant_id: str) -> DeadlineRepository:
        """
        Get or create a deadline repository for the given tenant.

        Args:
            tenant_id: Tenant ID

        Returns:
            DeadlineRepository instance
        """
        if tenant_id not in self._repositories:
            self._repositories[tenant_id] = DeadlineRepository(
                tenant_id=tenant_id,
                use_service_role=True  # Use service role for agent operations
            )

        return self._repositories[tenant_id]
    
    def _generate_cache_key(self, tenant_id: str, analysis_type: str, matter_ids: List[str]) -> str:
        """
        Generate a cache key for analysis results.
        
        Args:
            tenant_id: Tenant ID
            analysis_type: Type of analysis
            matter_ids: List of matter IDs
            
        Returns:
            Cache key string
        """
        key_data = {
            "tenant_id": tenant_id,
            "analysis_type": analysis_type,
            "matter_ids": sorted(matter_ids)  # Sort for consistent keys
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_entry: Dict[str, Any]) -> bool:
        """
        Check if a cache entry is still valid.
        
        Args:
            cache_entry: Cache entry with timestamp
            
        Returns:
            True if cache entry is valid
        """
        current_time = datetime.now(timezone.utc).timestamp() * 1000
        return (current_time - cache_entry["timestamp"]) < self._cache_ttl_ms
    
    async def fetch_deadline_data(self, state: DeadlineInsightsState, config: Any = None) -> DeadlineInsightsState:
        """
        Fetch deadline data for analysis.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state with fetched data
        """
        logger.info("Fetching deadline data for analysis")

        try:
            # Validate tenant ID
            if not state.tenant_id:
                logger.error("No tenant ID provided for deadline data fetching")
                state.error = "Tenant ID is required for deadline data fetching"
                return state

            # Get repository for this tenant
            repository = self._get_repository(state.tenant_id)

            # Fetch deadlines based on analysis type and configuration
            if state.matter_ids:
                # Fetch deadlines for specific matters
                logger.info(f"Fetching deadlines for {len(state.matter_ids)} matters")
                deadlines = await repository.get_deadlines_by_matter_ids(
                    matter_ids=state.matter_ids,
                    limit=state.batch_config.batch_size,
                    include_metadata=True
                )
            else:
                # Fetch all deadlines for the tenant (with limits)
                logger.info("Fetching all deadlines for tenant")
                deadlines = await repository.get_all_deadlines(
                    limit=state.batch_config.batch_size,
                    priority_filter=None,  # Could be configured based on analysis type
                    date_range=None  # Could be configured for time-based analysis
                )

            # Store fetched data in state
            state.deadline_data = deadlines
            state.data_fetched = True
            state.status = "data_fetched"

            # Update cache statistics
            state.cache_misses += 1  # This was a database fetch

            logger.info(f"Successfully fetched {len(deadlines)} deadlines for analysis")

        except Exception as e:
            logger.error(f"Error fetching deadline data: {e}")
            state.error = f"Failed to fetch deadline data: {str(e)}"
            state.status = "failed"
            state.data_fetched = False
        
        return state
    
    async def detect_conflicts(self, state: DeadlineInsightsState, config: Any = None) -> DeadlineInsightsState:
        """
        Detect conflicts between deadlines.
        
        Args:
            state: Current state
            config: Runnable configuration
            
        Returns:
            Updated state with conflict detection results
        """
        logger.info("Detecting deadline conflicts")
        
        try:
            # Validate that data has been fetched
            if not state.data_fetched or not state.deadline_data:
                logger.error("No deadline data available for conflict detection")
                state.error = "No deadline data available for conflict detection"
                return state

            # TODO: Implement sophisticated conflict detection algorithms
            # This will be enhanced in Task 3: Core analysis algorithms

            # Basic conflict detection using fetched data
            deadlines = state.deadline_data
            conflicts = []

            # Simple date-based conflict detection
            date_groups = {}
            for deadline in deadlines:
                if deadline.get('date'):
                    date_key = deadline['date']
                    if date_key not in date_groups:
                        date_groups[date_key] = []
                    date_groups[date_key].append(deadline)

            # Detect conflicts where multiple high-priority deadlines share the same date
            for date_key, date_deadlines in date_groups.items():
                if len(date_deadlines) > 1:
                    high_priority_deadlines = [
                        d for d in date_deadlines
                        if d.get('priority') in ['critical', 'high']
                    ]

                    if len(high_priority_deadlines) > 1:
                        conflict = DeadlineConflict(
                            id=str(uuid.uuid4()),
                            deadline_ids=[d['id'] for d in high_priority_deadlines],
                            severity=ConflictSeverity.HIGH,
                            description=f"Multiple high-priority deadlines on {date_key}",
                            suggested_resolution="Review and prioritize deadlines for this date"
                        )
                        conflicts.append(conflict)

            # Create analysis results
            analysis_id = str(uuid.uuid4())
            analysis = DeadlineAnalysis(
                analysis_id=analysis_id,
                analysis_type=AnalysisType.CONFLICT_DETECTION,
                tenant_id=state.tenant_id or "unknown",
                matter_ids=state.matter_ids,
                total_deadlines=len(deadlines),
                conflicts=conflicts,
                recommendations=[
                    f"Analyzed {len(deadlines)} deadlines",
                    f"Found {len(conflicts)} potential conflicts",
                    "Consider reviewing conflicting deadlines for prioritization"
                ]
            )

            state.analysis = analysis
            state.conflicts = conflicts
            state.analysis_completed = True
            state.status = "conflicts_detected"
            
            logger.info(f"Conflict detection completed: {analysis_id}")
            
        except Exception as e:
            logger.error(f"Failed to detect conflicts: {e}")
            state.error = f"Failed to detect conflicts: {str(e)}"
            state.status = "failed"
        
        return state
    
    async def assess_risks(self, state: DeadlineInsightsState, config: Any = None) -> DeadlineInsightsState:
        """
        Assess risks for deadlines.
        
        Args:
            state: Current state
            config: Runnable configuration
            
        Returns:
            Updated state with risk assessment results
        """
        logger.info("Assessing deadline risks")
        
        try:
            # TODO: Implement risk assessment algorithms
            # This will be implemented in Task 3: Core analysis algorithms
            
            # For now, create a placeholder analysis
            analysis_id = str(uuid.uuid4())
            analysis = DeadlineAnalysis(
                analysis_id=analysis_id,
                analysis_type=AnalysisType.RISK_ASSESSMENT,
                tenant_id=state.tenant_id or "unknown",
                matter_ids=state.matter_ids,
                total_deadlines=0,  # Will be populated with real data
                high_risk_deadlines=[],  # Will be populated with high-risk deadlines
                recommendations=["Risk assessment analysis completed"]
            )
            
            state.analysis = analysis
            state.analysis_completed = True
            state.status = "risks_assessed"
            
            logger.info(f"Risk assessment completed: {analysis_id}")
            
        except Exception as e:
            logger.error(f"Failed to assess risks: {e}")
            state.error = f"Failed to assess risks: {str(e)}"
            state.status = "failed"
        
        return state
    
    async def track_sol_deadlines(self, state: DeadlineInsightsState, config: Any = None) -> DeadlineInsightsState:
        """
        Track statute of limitations deadlines using MCP Rules Engine.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state with SOL tracking results
        """
        logger.info("Tracking SOL deadlines")

        try:
            if not ENABLE_MCP_INTEGRATION:
                logger.warning("MCP Rules Engine integration is disabled")
                state.error = "MCP Rules Engine integration is disabled"
                return state

            if not state.tenant_id:
                logger.error("Tenant ID is required for SOL tracking")
                state.error = "Tenant ID is required for SOL tracking"
                state.status = "failed"
                return state

            # Import MCP client
            from .mcp_client import init_mcp_client

            # Initialize MCP client for this tenant
            mcp_client = await init_mcp_client(state.tenant_id)

            # Get repository for matter data
            repository = self._get_repository(state.tenant_id)

            # Fetch matter data for SOL analysis
            matters_data = await self._fetch_matters_for_sol_tracking(repository, state)

            if not matters_data:
                logger.warning("No matters found for SOL tracking")
                analysis = DeadlineAnalysis(
                    analysis_id=str(uuid.uuid4()),
                    analysis_type=AnalysisType.SOL_TRACKING,
                    tenant_id=state.tenant_id,
                    matter_ids=state.matter_ids,
                    total_deadlines=0,
                    recommendations=["No matters found for SOL tracking"]
                )
                state.analysis = analysis
                state.analysis_completed = True
                state.status = "sol_tracked"
                return state

            # Process matters and calculate SOL deadlines
            sol_results = []
            total_deadlines = 0
            high_risk_deadlines = []
            recommendations = []

            async with mcp_client:
                for matter in matters_data:
                    try:
                        # Extract SOL parameters from matter
                        sol_params = self._extract_sol_parameters(matter)

                        if not sol_params:
                            logger.warning(f"Could not extract SOL parameters for matter {matter.get('id')}")
                            continue

                        # Calculate SOL deadlines using MCP Rules Engine
                        mcp_response = await mcp_client.calculate_deadlines(
                            jurisdiction=sol_params["jurisdiction"],
                            trigger_code=sol_params["trigger_code"],
                            start_date=sol_params["start_date"],
                            practice_area=sol_params["practice_area"]
                        )

                        # Process MCP response
                        matter_deadlines = self._process_mcp_response(matter, mcp_response)
                        sol_results.extend(matter_deadlines)
                        total_deadlines += len(matter_deadlines)

                        # Identify high-risk deadlines (within 90 days)
                        for deadline in matter_deadlines:
                            if self._is_high_risk_deadline(deadline):
                                high_risk_deadlines.append(deadline["id"])

                        logger.info(f"Calculated {len(matter_deadlines)} SOL deadlines for matter {matter.get('id')}")

                    except Exception as e:
                        logger.error(f"Failed to calculate SOL for matter {matter.get('id')}: {str(e)}")
                        continue

            # Generate recommendations based on results
            recommendations = self._generate_sol_recommendations(sol_results, high_risk_deadlines)

            # Create analysis result
            analysis_id = str(uuid.uuid4())
            analysis = DeadlineAnalysis(
                analysis_id=analysis_id,
                analysis_type=AnalysisType.SOL_TRACKING,
                tenant_id=state.tenant_id,
                matter_ids=state.matter_ids,
                total_deadlines=total_deadlines,
                high_risk_deadlines=high_risk_deadlines,
                recommendations=recommendations
            )

            # Store SOL results in state for further processing
            state.analysis = analysis
            state.analysis_completed = True
            state.status = "sol_tracked"

            # Store detailed SOL results in metadata
            if not hasattr(state, 'metadata'):
                state.metadata = {}
            state.metadata["sol_results"] = sol_results

            logger.info(f"SOL tracking completed: {analysis_id}, {total_deadlines} deadlines calculated")

        except Exception as e:
            logger.error(f"Failed to track SOL deadlines: {e}")
            state.error = f"Failed to track SOL deadlines: {str(e)}"
            state.status = "failed"

        return state

    async def _fetch_matters_for_sol_tracking(self, repository, state: DeadlineInsightsState) -> List[Dict[str, Any]]:
        """
        Fetch matter data for SOL tracking analysis.

        Args:
            repository: Database repository instance
            state: Current state with matter IDs

        Returns:
            List of matter data dictionaries
        """
        try:
            # Use the repository's database connection to fetch matter data
            # This is a simplified implementation - in practice, you'd want to use
            # the repository's connection or create a dedicated matter repository

            if state.matter_ids:
                # Fetch specific matters
                logger.info(f"Fetching {len(state.matter_ids)} specific matters for SOL tracking")
                # For now, return mock data structure
                # In real implementation, this would query the database
                matters = []
                for matter_id in state.matter_ids:
                    matters.append({
                        "id": matter_id,
                        "title": f"Matter {matter_id}",
                        "practice_area": "personal_injury",
                        "work_type": "litigation",
                        "jurisdiction": "TX",
                        "filing_date": "2023-01-15",
                        "opened_date": "2023-01-10",
                        "metadata": {
                            "incident_date": "2022-12-01",
                            "case_type": "motor_vehicle_accident"
                        }
                    })
                return matters
            else:
                # Fetch all active litigation matters for the tenant
                logger.info("Fetching all active litigation matters for SOL tracking")
                # Mock implementation - would query database for active litigation matters
                return [{
                    "id": "sample-matter-1",
                    "title": "Sample Personal Injury Case",
                    "practice_area": "personal_injury",
                    "work_type": "litigation",
                    "jurisdiction": "TX",
                    "filing_date": "2023-01-15",
                    "opened_date": "2023-01-10",
                    "metadata": {
                        "incident_date": "2022-12-01",
                        "case_type": "motor_vehicle_accident"
                    }
                }]

        except Exception as e:
            logger.error(f"Failed to fetch matters for SOL tracking: {str(e)}")
            return []

    def _extract_sol_parameters(self, matter: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """
        Extract SOL calculation parameters from matter data.

        Args:
            matter: Matter data dictionary

        Returns:
            Dictionary with SOL parameters or None if extraction fails
        """
        try:
            # Extract jurisdiction (default to TX if not specified)
            jurisdiction = matter.get("jurisdiction", "TX")

            # Map practice area to MCP practice area
            practice_area = self._map_practice_area(matter.get("practice_area", "personal_injury"))

            # Determine trigger code based on case type and practice area
            trigger_code = self._determine_trigger_code(matter)

            # Determine start date for SOL calculation
            start_date = self._determine_sol_start_date(matter)

            if not all([jurisdiction, trigger_code, start_date]):
                logger.warning(f"Missing required SOL parameters for matter {matter.get('id')}")
                return None

            return {
                "jurisdiction": jurisdiction,
                "trigger_code": trigger_code,
                "start_date": start_date,
                "practice_area": practice_area
            }

        except Exception as e:
            logger.error(f"Failed to extract SOL parameters from matter {matter.get('id')}: {str(e)}")
            return None

    def _map_practice_area(self, practice_area: str) -> str:
        """Map internal practice area to MCP practice area."""
        mapping = {
            "personal_injury": "personal_injury",
            "family_law": "family_law",
            "criminal_defense": "criminal_defense",
            "civil_litigation": "civil_litigation",
            "employment_law": "employment_law",
            "corporate_business": "corporate_business",
            "real_estate": "real_estate",
            "estate_planning": "estate_planning",
            "intellectual_property": "intellectual_property"
        }
        return mapping.get(practice_area, "personal_injury")

    def _determine_trigger_code(self, matter: Dict[str, Any]) -> str:
        """
        Determine the appropriate trigger code for SOL calculation.

        Args:
            matter: Matter data dictionary

        Returns:
            Trigger code string
        """
        # Check metadata for specific case type
        metadata = matter.get("metadata", {})
        case_type = metadata.get("case_type", "")

        # Map case types to trigger codes
        trigger_mapping = {
            "motor_vehicle_accident": "ACCIDENT_DATE",
            "slip_and_fall": "ACCIDENT_DATE",
            "medical_malpractice": "DISCOVERY_DATE",
            "product_liability": "INJURY_DATE",
            "wrongful_death": "DEATH_DATE",
            "workers_compensation": "INJURY_DATE",
            "premises_liability": "ACCIDENT_DATE"
        }

        # Get trigger code from case type
        trigger_code = trigger_mapping.get(case_type)

        if trigger_code:
            return trigger_code

        # Default based on practice area
        practice_area = matter.get("practice_area", "")
        if practice_area == "personal_injury":
            return "ACCIDENT_DATE"
        elif practice_area == "criminal_defense":
            return "CHARGE_DATE"
        elif practice_area == "family_law":
            return "SERVICE_DATE"
        else:
            return "INCIDENT_DATE"

    def _determine_sol_start_date(self, matter: Dict[str, Any]) -> Optional[str]:
        """
        Determine the start date for SOL calculation.

        Args:
            matter: Matter data dictionary

        Returns:
            Start date in YYYY-MM-DD format or None
        """
        metadata = matter.get("metadata", {})

        # Priority order for date selection
        date_fields = [
            "incident_date",
            "accident_date",
            "injury_date",
            "discovery_date",
            "filing_date",
            "opened_date"
        ]

        # Try to find a valid date
        for field in date_fields:
            date_value = metadata.get(field) or matter.get(field)
            if date_value:
                # Ensure date is in YYYY-MM-DD format
                if isinstance(date_value, str) and len(date_value) >= 10:
                    return date_value[:10]  # Take first 10 characters (YYYY-MM-DD)

        # Fallback to current date minus 1 year (conservative estimate)
        from datetime import datetime, timedelta
        fallback_date = datetime.now() - timedelta(days=365)
        return fallback_date.strftime("%Y-%m-%d")

    def _process_mcp_response(self, matter: Dict[str, Any], mcp_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process MCP Rules Engine response into standardized deadline format.

        Args:
            matter: Matter data dictionary
            mcp_response: Response from MCP Rules Engine

        Returns:
            List of processed deadline dictionaries
        """
        deadlines = []

        try:
            mcp_deadlines = mcp_response.get("deadlines", [])

            for mcp_deadline in mcp_deadlines:
                deadline = {
                    "id": f"sol_{matter.get('id')}_{mcp_deadline.get('id', str(uuid.uuid4()))}",
                    "matter_id": matter.get("id"),
                    "title": mcp_deadline.get("name", "SOL Deadline"),
                    "description": mcp_deadline.get("description", ""),
                    "due_date": mcp_deadline.get("dueDate"),
                    "priority": mcp_deadline.get("priority", "high"),
                    "category": mcp_deadline.get("category", "statute_of_limitations"),
                    "legal_basis": mcp_deadline.get("legalBasis", ""),
                    "consequences": mcp_deadline.get("consequences", ""),
                    "source": "mcp_rules_engine",
                    "jurisdiction": mcp_response.get("jurisdiction"),
                    "trigger_code": mcp_response.get("triggerCode"),
                    "calculated_at": mcp_response.get("calculatedAt"),
                    "metadata": {
                        "matter_title": matter.get("title"),
                        "practice_area": matter.get("practice_area"),
                        "work_type": matter.get("work_type")
                    }
                }
                deadlines.append(deadline)

        except Exception as e:
            logger.error(f"Failed to process MCP response for matter {matter.get('id')}: {str(e)}")

        return deadlines

    def _is_high_risk_deadline(self, deadline: Dict[str, Any]) -> bool:
        """
        Determine if a deadline is high-risk (approaching soon).

        Args:
            deadline: Deadline dictionary

        Returns:
            True if deadline is high-risk
        """
        try:
            due_date_str = deadline.get("due_date")
            if not due_date_str:
                return False

            from datetime import datetime, timedelta

            # Parse due date
            due_date = datetime.fromisoformat(due_date_str.replace('Z', '+00:00'))
            current_date = datetime.now(due_date.tzinfo)

            # Consider high-risk if within 90 days
            days_until_due = (due_date - current_date).days

            return days_until_due <= 90 and days_until_due >= 0

        except Exception as e:
            logger.error(f"Failed to assess deadline risk: {str(e)}")
            return False

    def _generate_sol_recommendations(self, sol_results: List[Dict[str, Any]], high_risk_deadlines: List[str]) -> List[str]:
        """
        Generate recommendations based on SOL tracking results.

        Args:
            sol_results: List of calculated SOL deadlines
            high_risk_deadlines: List of high-risk deadline IDs

        Returns:
            List of recommendation strings
        """
        recommendations = []

        if not sol_results:
            recommendations.append("No SOL deadlines calculated - verify matter data completeness")
            return recommendations

        # High-risk deadline recommendations
        if high_risk_deadlines:
            recommendations.append(f"URGENT: {len(high_risk_deadlines)} SOL deadlines approaching within 90 days")
            recommendations.append("Review high-risk matters immediately and prioritize filing actions")

        # General recommendations based on results
        total_deadlines = len(sol_results)
        recommendations.append(f"Monitoring {total_deadlines} statute of limitations deadlines")

        # Practice area specific recommendations
        practice_areas = set(deadline.get("metadata", {}).get("practice_area") for deadline in sol_results)
        if "personal_injury" in practice_areas:
            recommendations.append("Personal injury cases: Verify incident dates and discovery rules")

        if "medical_malpractice" in practice_areas:
            recommendations.append("Medical malpractice cases: Confirm discovery date vs incident date rules")

        # Jurisdiction specific recommendations
        jurisdictions = set(deadline.get("jurisdiction") for deadline in sol_results)
        if len(jurisdictions) > 1:
            recommendations.append(f"Multi-jurisdiction cases detected: {', '.join(jurisdictions)} - verify local rules")

        recommendations.append("Set up automated reminders for all SOL deadlines")
        recommendations.append("Review and update matter metadata to improve SOL tracking accuracy")

        return recommendations

    async def process_batch_analysis(self, state: DeadlineInsightsState, config: Any = None) -> DeadlineInsightsState:
        """
        Process deadlines in batches for large datasets with MCP integration.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state with batch processing results
        """
        logger.info("Processing batch analysis with MCP integration")

        try:
            if not state.tenant_id:
                logger.error("Tenant ID is required for batch analysis")
                state.error = "Tenant ID is required for batch analysis"
                state.status = "failed"
                return state

            # Get repository for data access
            repository = self._get_repository(state.tenant_id)

            # Fetch matters for batch processing
            matters_data = await self._fetch_matters_for_sol_tracking(repository, state)

            if not matters_data:
                logger.warning("No matters found for batch analysis")
                analysis = DeadlineAnalysis(
                    analysis_id=str(uuid.uuid4()),
                    analysis_type=AnalysisType.BATCH_ANALYSIS,
                    tenant_id=state.tenant_id,
                    matter_ids=state.matter_ids,
                    total_deadlines=0,
                    recommendations=["No matters found for batch analysis"]
                )
                state.analysis = analysis
                state.analysis_completed = True
                state.status = "batch_processed"
                return state

            # Calculate batch configuration
            total_matters = len(matters_data)
            batch_size = state.batch_config.batch_size
            state.total_batches = max(1, (total_matters + batch_size - 1) // batch_size)

            logger.info(f"Processing {total_matters} matters in {state.total_batches} batches of size {batch_size}")

            # Initialize batch processing results
            all_deadlines = []
            all_conflicts = []
            high_risk_deadlines = []
            batch_errors = []
            processed_matters = 0

            # Process matters in batches
            if ENABLE_MCP_INTEGRATION:
                from .mcp_client import init_mcp_client
                mcp_client = await init_mcp_client(state.tenant_id)

                async with mcp_client:
                    for batch_num in range(state.total_batches):
                        state.current_batch = batch_num + 1

                        # Get batch of matters
                        start_idx = batch_num * batch_size
                        end_idx = min(start_idx + batch_size, total_matters)
                        batch_matters = matters_data[start_idx:end_idx]

                        logger.info(f"Processing batch {state.current_batch}/{state.total_batches} ({len(batch_matters)} matters)")

                        # Process batch with error handling and retry logic
                        batch_results = await self._process_batch_with_mcp(
                            batch_matters, mcp_client, state.current_batch
                        )

                        # Aggregate results
                        all_deadlines.extend(batch_results.get("deadlines", []))
                        all_conflicts.extend(batch_results.get("conflicts", []))
                        high_risk_deadlines.extend(batch_results.get("high_risk_deadlines", []))
                        batch_errors.extend(batch_results.get("errors", []))
                        processed_matters += batch_results.get("processed_count", 0)

                        # Update processed deadlines tracking
                        for deadline in batch_results.get("deadlines", []):
                            state.processed_deadlines.add(deadline.get("id", ""))

                        # Add small delay between batches to avoid overwhelming the API
                        if batch_num < state.total_batches - 1:
                            await asyncio.sleep(0.5)
            else:
                logger.warning("MCP integration disabled - using mock batch processing")
                # Mock processing for when MCP is disabled
                processed_matters = total_matters
                all_deadlines = [{"id": f"mock_deadline_{i}", "title": f"Mock Deadline {i}"} for i in range(total_matters)]

            # Generate batch analysis recommendations
            recommendations = self._generate_batch_recommendations(
                processed_matters, len(all_deadlines), len(high_risk_deadlines), batch_errors
            )

            # Create comprehensive analysis result
            analysis_id = str(uuid.uuid4())
            analysis = DeadlineAnalysis(
                analysis_id=analysis_id,
                analysis_type=AnalysisType.BATCH_ANALYSIS,
                tenant_id=state.tenant_id,
                matter_ids=state.matter_ids,
                total_deadlines=len(all_deadlines),
                conflicts=all_conflicts,
                high_risk_deadlines=high_risk_deadlines,
                recommendations=recommendations
            )

            # Store detailed results in state
            state.analysis = analysis
            state.analysis_completed = True
            state.status = "batch_processed"

            # Store batch results in metadata
            if not hasattr(state, 'metadata'):
                state.metadata = {}
            state.metadata.update({
                "batch_results": {
                    "total_matters_processed": processed_matters,
                    "total_deadlines_calculated": len(all_deadlines),
                    "high_risk_count": len(high_risk_deadlines),
                    "error_count": len(batch_errors),
                    "batches_completed": state.current_batch
                },
                "all_deadlines": all_deadlines[:100],  # Store first 100 for reference
                "batch_errors": batch_errors
            })

            logger.info(f"Batch analysis completed: {analysis_id}, processed {processed_matters} matters, calculated {len(all_deadlines)} deadlines")

        except Exception as e:
            logger.error(f"Failed to process batch analysis: {e}")
            state.error = f"Failed to process batch analysis: {str(e)}"
            state.status = "failed"

        return state

    async def _process_batch_with_mcp(
        self, batch_matters: List[Dict[str, Any]], mcp_client, batch_num: int
    ) -> Dict[str, Any]:
        """
        Process a batch of matters with MCP Rules Engine integration.

        Args:
            batch_matters: List of matter dictionaries to process
            mcp_client: Initialized MCP client
            batch_num: Current batch number for logging

        Returns:
            Dictionary with batch processing results
        """
        results = {
            "deadlines": [],
            "conflicts": [],
            "high_risk_deadlines": [],
            "errors": [],
            "processed_count": 0
        }

        for matter in batch_matters:
            try:
                # Extract SOL parameters
                sol_params = self._extract_sol_parameters(matter)

                if not sol_params:
                    results["errors"].append(f"Could not extract SOL parameters for matter {matter.get('id')}")
                    continue

                # Calculate deadlines with retry logic
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        mcp_response = await mcp_client.calculate_deadlines(
                            jurisdiction=sol_params["jurisdiction"],
                            trigger_code=sol_params["trigger_code"],
                            start_date=sol_params["start_date"],
                            practice_area=sol_params["practice_area"]
                        )

                        # Process successful response
                        matter_deadlines = self._process_mcp_response(matter, mcp_response)
                        results["deadlines"].extend(matter_deadlines)

                        # Check for high-risk deadlines
                        for deadline in matter_deadlines:
                            if self._is_high_risk_deadline(deadline):
                                results["high_risk_deadlines"].append(deadline["id"])

                        # Check for conflicts (simplified implementation)
                        conflicts = self._detect_deadline_conflicts(matter_deadlines)
                        results["conflicts"].extend(conflicts)

                        results["processed_count"] += 1
                        break  # Success, exit retry loop

                    except Exception as retry_error:
                        if attempt < max_retries - 1:
                            logger.warning(f"Retry {attempt + 1} for matter {matter.get('id')}: {str(retry_error)}")
                            await asyncio.sleep(1 * (attempt + 1))  # Exponential backoff
                        else:
                            results["errors"].append(f"Failed to process matter {matter.get('id')} after {max_retries} attempts: {str(retry_error)}")

            except Exception as e:
                results["errors"].append(f"Error processing matter {matter.get('id')}: {str(e)}")
                logger.error(f"Batch {batch_num}: Error processing matter {matter.get('id')}: {str(e)}")

        logger.info(f"Batch {batch_num}: Processed {results['processed_count']}/{len(batch_matters)} matters, {len(results['deadlines'])} deadlines calculated")
        return results

    def _detect_deadline_conflicts(self, deadlines: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Detect conflicts between deadlines (simplified implementation).

        Args:
            deadlines: List of deadline dictionaries

        Returns:
            List of conflict dictionaries
        """
        conflicts = []

        # Simple conflict detection: deadlines on the same day
        date_groups = {}
        for deadline in deadlines:
            due_date = deadline.get("due_date")
            if due_date:
                if due_date not in date_groups:
                    date_groups[due_date] = []
                date_groups[due_date].append(deadline)

        # Report conflicts for dates with multiple deadlines
        for date, date_deadlines in date_groups.items():
            if len(date_deadlines) > 1:
                conflicts.append({
                    "type": "same_day_deadlines",
                    "date": date,
                    "deadline_ids": [d["id"] for d in date_deadlines],
                    "severity": "medium",
                    "description": f"{len(date_deadlines)} deadlines due on the same day"
                })

        return conflicts

    def _generate_batch_recommendations(
        self, processed_matters: int, total_deadlines: int, high_risk_count: int, errors: List[str]
    ) -> List[str]:
        """
        Generate recommendations based on batch processing results.

        Args:
            processed_matters: Number of matters successfully processed
            total_deadlines: Total deadlines calculated
            high_risk_count: Number of high-risk deadlines
            errors: List of error messages

        Returns:
            List of recommendation strings
        """
        recommendations = []

        # Processing summary
        recommendations.append(f"Batch processing completed: {processed_matters} matters processed, {total_deadlines} deadlines calculated")

        # High-risk deadline warnings
        if high_risk_count > 0:
            recommendations.append(f"URGENT: {high_risk_count} high-risk deadlines require immediate attention")
            recommendations.append("Review high-risk deadlines and set up priority reminders")

        # Error handling recommendations
        if errors:
            error_count = len(errors)
            recommendations.append(f"WARNING: {error_count} processing errors occurred")
            recommendations.append("Review matter data quality and completeness")
            if error_count > processed_matters * 0.1:  # More than 10% error rate
                recommendations.append("High error rate detected - consider data validation improvements")

        # Performance recommendations
        if processed_matters > 50:
            recommendations.append("Large dataset processed - consider implementing automated monitoring")

        # General recommendations
        recommendations.append("Set up automated deadline monitoring and reminder systems")
        recommendations.append("Regularly update matter metadata to improve deadline accuracy")

        return recommendations

    async def comprehensive_analysis(self, state: DeadlineInsightsState, config: Any = None) -> DeadlineInsightsState:
        """
        Perform comprehensive deadline analysis combining all analysis types.
        
        Args:
            state: Current state
            config: Runnable configuration
            
        Returns:
            Updated state with comprehensive analysis results
        """
        logger.info("Performing comprehensive deadline analysis")
        
        try:
            # TODO: Implement comprehensive analysis combining all analysis types
            # This will be implemented in Task 3: Core analysis algorithms
            
            # For now, create a comprehensive placeholder analysis
            analysis_id = str(uuid.uuid4())
            analysis = DeadlineAnalysis(
                analysis_id=analysis_id,
                analysis_type=AnalysisType.COMPREHENSIVE,
                tenant_id=state.tenant_id or "unknown",
                matter_ids=state.matter_ids,
                total_deadlines=0,  # Will be populated with real data
                conflicts=[],  # Will be populated with detected conflicts
                high_risk_deadlines=[],  # Will be populated with high-risk deadlines
                recommendations=[
                    "Comprehensive analysis completed",
                    "All deadline analysis types have been performed",
                    "Review conflicts and high-risk deadlines for immediate attention"
                ]
            )
            
            state.analysis = analysis
            state.analysis_completed = True
            state.status = "comprehensive_completed"
            
            logger.info(f"Comprehensive analysis completed: {analysis_id}")
            
        except Exception as e:
            logger.error(f"Failed to perform comprehensive analysis: {e}")
            state.error = f"Failed to perform comprehensive analysis: {str(e)}"
            state.status = "failed"
        
        return state
